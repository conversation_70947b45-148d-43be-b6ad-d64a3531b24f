<?php
// VDO.Ninja PHP Application
// Based on default.html structure with PHP functionality

// Set content type to HTML
header('Content-Type: text/html; charset=utf-8');

// Optional: Add any PHP logic here for dynamic content
$page_title = "VDO.Ninja";
$page_description = "Bring live video from your smartphone, computer, or friends directly into your Studio. 100% free.";
$author = "Steve Seguin";
$current_year = date('Y');
?>
<!DOCTYPE html>
<html lang='en'>
<head>
    <script type="text/javascript">
        //  MS Internet Explorer must not be given a chance to fail before I can give the user an error message.
        try {
            var msie = window.navigator.userAgent.indexOf("MSIE ");
            if (msie>0 || !!navigator.userAgent.match(/Trident.*rv\:11\./)){ // If MSIE or IE 11
                alert("Internet Explorer is not supported.\n\nPlease consider using Microsoft Edge or Google Chrome instead\n\nYou will be forwarded to the download page for MS Edge now.");
                console.error("INTERNET EXPLORER IS EVIL");
                document.write("Internet Explorer is not supported");
                window.location = "https://www.microsoft.com/edge";
            }
        } catch(e){
            console.error(e);
        }
    </script>
    <style>
        html {
            background-color: #0000;
            transition: opacity .1s linear;
        }
    </style>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta content="text/html;charset=utf-8" http-equiv="Content-Type" />
    <meta content="utf-8" http-equiv="encoding" />
    <meta name="copyright" content="&copy; <?php echo $current_year; ?> <?php echo htmlspecialchars($author); ?>" />
    <meta name="license" content="https://github.com/steveseguin/vdo.ninja/LICENSE.md" />
    <meta name="sourcecode" content="https://github.com/steveseguin/vdo.ninja" />
    <meta name="stance-on-war" content="Steve Seguin condemns Russia's brutal invasion of Ukraine 💙💛." />
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://vdo.ninja/">
    <link rel="author" href="/about" />
    <link rel="me" href="https://vdo.ninja/about" />
    
    <!-- Primary Meta Tags -->
    <title><?php echo htmlspecialchars($page_title); ?></title>
    <meta id="metaTitle" name="title" content="<?php echo htmlspecialchars($page_title); ?>" />
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>" />
    <meta name="author" content="<?php echo htmlspecialchars($author); ?>" />
    
    <meta name="msapplication-TileColor" content="#da532c" />
    <meta name="theme-color" content="#0f131d" />
    <link rel="stylesheet" href="./main.css?ver=401" />
    <script type="text/javascript" crossorigin="anonymous" src="./thirdparty/adapter.js"></script>
    
    <link rel="shortcut icon" href="data:image/x-icon;," type="image/x-icon" />
    <link id="favicon1" rel="icon" type="image/png" sizes="32x32" href="./media/favicon-32x32.png" />
    <link id="favicon2" rel="icon" type="image/png" sizes="16x16" href="./media/favicon-16x16.png" />
    <link id="favicon3" rel="icon" href="./media/favicon.ico" />
    <link id="thumbnailUrl" itemprop="thumbnailUrl" href="./media/vdoNinja_logo_full.png" />
    <link rel="alternate" type="text/markdown" href="/rawdoc.md" title="Developer and User Documentation">

    <!-- X (Twitter) Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:creator" content="@SteveSeguin">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta name="twitter:description" content="Bring live video from your smartphone, computer, or friends directly into OBS Studio or other. 100% free.">
    <meta name="twitter:image" content="https://vdo.ninja/media/vdoNinja_logo_full.png">
    
    <!-- Open Graph Tags (also used by X) -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://vdo.ninja/">
    <meta property="og:title" content="<?php echo htmlspecialchars($page_title); ?>">
    <meta property="og:description" content="Bring live video from your smartphone, computer, or friends directly into OBS Studio or other. 100% free.">
    <meta property="og:image" content="https://vdo.ninja/media/vdoNinja_logo_full.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="<?php echo htmlspecialchars($page_title); ?>">

    <style id="lightbox-animations" type="text/css"></style>
</head>
<body id="main" class="main hidden" onload="main()">
    <span itemprop="image" itemscope itemtype="http://schema.org/ImageObject">
        <link itemprop="url" href="./media/vdoNinja_logo_full.png" />
    </span>
    <link itemprop="thumbnailUrl" href="./media/vdoNinja_logo_full.png" />
    <span itemprop="thumbnail" itemscope itemtype="http://schema.org/ImageObject">
        <link itemprop="url" href="./media/vdoNinja_logo_full.png" />
    </span>
    <script type="text/javascript" crossorigin="anonymous"  src="./thirdparty/CodecsHandler.js?ver=28"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./thirdparty/aes.js"></script>
    <script type="text/javascript" crossorigin="anonymous" src="./webrtc.js?ver=842"></script>
    <input id="zoomSlider" type="range" style="display: none;" />
    <span id="electronDragZone" style="pointer-events: none; z-index:-10; position:absolute;top:0;left:0;width:100%;height:2%;-webkit-app-region: drag;min-height:20px;"></span>

    <!-- Header Section -->
    <div id="header">
        <div id="head5" class="hidden"></div>
        <div id="head3" style="display: inline-block;" class="hidden">
            <span style="color: #888;" id="copythisurl" tabindex="1" > &nbsp;
                <span data-translate="copy-this-url">Copy this URL into an OBS "Browser Source"</span> <i style="color: #CCC;" class="las la-long-arrow-alt-right"></i> &nbsp;
            </span>
        </div>
        <div id="head3a" style="display: inline-block;" class="hidden">
            <a
                id="reshare"
                data-drag="1"
                onclick="copyFunction(this, event)"
                class="task grabLinks"
                data-menu="context-menu"
                style="font-weight: bold; color: #afa !important; cursor: grab; background-color: #0000;  font-size: 115%; min-width: 335px; max-width: 800px;"
            ></a>
            <i class="las la-paperclip" style="color: #DDD;" title="Copy link to clipboard" onclick="copyFunction(document.getElementById('reshare'), event);" onmouseover="this.style.cursor='pointer'"></i>
            <span title="Save and ask to reload the current page on next site visit" style='font-size:92%;' onclick="saveRoom(this);" onmouseover="this.style.cursor='pointer'">💾</span>
        </div>
        <div id="head4" style="display: inline-block;" class="hidden">
            <span style="font-size: 68%; color: white;">
                <span data-translate="you-are-in-the-control-center">Control center for room:</span>

                <div id="dirroomid" style="font-size: 140%; color: #99c; display: inline-block;"></div>
                <span id="saveRoom" onclick="saveRoom(this)" style='cursor:pointer;margin-left:10px;' title="Will remember the room, prompting you the next time you visit if you wish to load this director's room again">💾</span>
                <span id="togglePreviewMode" onclick="switchModes()" style='cursor:pointer;margin-left:2px;' title="Toggle between the director control-room view and a scene preview-mode.">🪟</span>
            </span>
        </div>
        <div id="head2" class="hidden" style="display: inline-block; text-decoration: none; font-size: 60%; color: white;">
            <span data-translate="joining-room">You are in room</span>:
            <div id="roomid" style="display: inline-block;"></div>
        </div>
        <div id="head6" class="hidden" data-translate="only-director-can-hear-you">Only the director can hear you currently.</div>
        <div id="head7" class="hidden" data-translate="director-muted-you">The director has muted you.</div>
        <div id="head8" class="hidden" data-translate="director-video-muted-you">The director has disabled your camera temporarily.</div>
    </div>

    <div id="obsState" class="hidden" >ACTIVE</div>

    <!-- Chat Module -->
    <div id="chatModule" class="hidden">
        <div class="chat-header">
            <span>Chat</span>
            <span>
                <a id="popOutChat" onclick="createPopoutChat();"><i class="las la-external-link-alt"></i></a>
                <a id="closeChat" onclick="toggleChat();">x</i></a>
            </span>
        </div>

        <div id="chatBody" class="resizable-div">
            <!-- Chat messages will be inserted here -->
        </div>

        <div class="chat-input-area">
            <input type="text" id="chatInput" placeholder="Enter chat message to send" onkeypress="EnterButtonChat(event)" />
            <button class="chatBarInputButton" onclick="sendChatMessage()">Send</button>
            <button onclick="toggleFileshare()"><i class="las la-file-upload"></i></button>
        </div>
        <div class="resizer"></div>
    </div>

    <div id="activeShares"></div>

    <!-- Control Buttons Section -->
    <div id="controlButtons" class="hidden">
        <div class="controlPositioning">
            <div id="subControlButtons">
                <div id="unmuteSelf" title="Hear yourself at 50% volume" alt="Hear yourself at 50% volume" aria-label="Hear Yourself" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleDirectFeedback()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                    <i id="unmuteSelftoggle" class="toggleSize las la-podcast"></i>
                </div>
                <div id="mediafileshare" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Select and stream a media file" aria-label="Stream a media file" alt="Stream a media file to others" onclick="getById('fileselector3').click();" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                    <i id="mediafilesharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-file-video"></i>
                    <input id="fileselector3" class="hidden" onchange="session.changePublishFile(this,event);" type="file" accept="video/*,audio/*" alt="Hold CTRL (or CMD) to select multiple files" title="Hold CTRL (or CMD) to select multiple files" multiple/>
                </div>

                <div id="blindAllGuests" title="Blind all guests in room (toggle)" alt="Blind all guests in room (toggle)" aria-label="Blind all guests in room" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="blindAllGuests(this, event)"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                    <i class="toggleSize las la-eye"></i>
                </div>

                <div id="queuebutton" title="Load the next guest in queue" alt="Load the next guest in queue"  aria-label="Load next guest in queue" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="nextQueue()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                    <i id="queuetoggle" class="toggleSize las la-stream"></i>
                    <div id="queueNotification"></div>
                </div>
                <div id="sharefilebutton" title="Transfer any file to the group" alt="Transfer any file to the group" aria-label="Select file to transfer" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleFileshare()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;" >
                    <i id="filesharetoggle" class="toggleSize las la-file-upload"></i>
                    <div id="transferNotification"></div>
                </div>
                <div id="chatbutton" title="Toggle the Chat" alt="Toggle the Chat" aria-label="Text chat" onmousedown="event.preventDefault(); event.stopPropagation();"  onclick="toggleChat()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                    <i id="chattoggle" class="toggleSize las la-comment-alt"></i>
                    <div id="chatNotification"></div>
                </div>
                <div id="mutespeakerbutton" onmousedown="event.preventDefault(); event.stopPropagation();" alt="Toggle the speaker output." aria-label="Mute Speaker output" title="Mute the Speaker (ALT + A)" onclick="toggleSpeakerMute()"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;" >
                    <i id="mutespeakertoggle" class="toggleSize las la-volume-up" style="position: relative; top: 0.5px;"></i>
                </div>
                <div id="mutebutton" onmousedown="toggleMute(false, event);event.preventDefault(); event.stopPropagation();" data-translate="mute-the-mic" title="Mute the Mic (CTRL/⌘ + M)" alt="Mute the Mic" aria-label="Mute Microphone" ontouchstart="toggleMute(false, event);event.preventDefault(); event.stopPropagation();" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;">
                    <i id="mutetoggle" class="toggleSize las la-microphone" style="position: relative; top: 0.5px;"></i>
                </div>
                <div id="mutevideobutton" onmousedown="event.preventDefault(); event.stopPropagation();" data-translate="disable-the-camera" title="Disable the Camera (CTRL/⌘ + B)" alt="Disable the Camera" aria-label="Mute Camera" onclick="toggleVideoMute()"   tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="hidden float" style="cursor: pointer;">
                    <i id="mutevideotoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-video"></i>
                </div>
                <div id="screensharebutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a Screen with others" alt="Share a Screen with others" aria-label="Share a screen" onclick="screenshareTypeDecider(1)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share" style="cursor: pointer;">
                    <i id="screensharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-desktop"></i>
                </div>
                <div id="screenshare2button" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Add a Screen Share" alt="Add a Screen Share" aria-label="Share a screen"  onclick="screenshareTypeDecider(2)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share"  style="cursor: pointer;">
                    <i id="screenshare2toggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las  la-tv"></i>
                </div>
                <div id="screenshare3button" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a Screen with others" alt="Add a Screen Share" aria-label="Share a screen"  onclick="screenshareTypeDecider(3)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden task" data-menu="context-menu-screen-share"  style="cursor: pointer;">
                    <i id="screenshare3toggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las  la-tv"></i>
                </div>
                <div id="websitesharebutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Share a website with your guests (IFRAME)" aria-label="Share a website" alt="Share a website with your guests (IFRAME)" onclick="shareWebsite(false, event)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                    <i id="websitesharetoggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-window-maximize"></i>
                </div>
                <div id="websitesharebutton2" onmousedown="event.preventDefault(); event.stopPropagation();" title="Hold CTRL (or CMD) and click to spotlight this video" alt="Share a website as an embedded iFRAME" aria-label="Share a website" onclick="shareWebsite(false, event)" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" class="float2 orange shake hidden" style="cursor: pointer;max-width: 200px;margin: auto;padding: 0 10px;">
                    <i onmousedown="event.preventDefault(); event.stopPropagation();" class="toggleSize las la-window-close" style="display: inline-block;"></i>
                    <div style="display: inline-block;width: 85px;line-height: 1; font-size: 0.9em; background-color: unset;box-shadow: unset;">
                        Stop Sharing Website
                    </div>
                </div>
                <div id="fullscreenPage" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Full-screen the page" alt="Full-screen the page" aria-label="Full screen"  onclick="fullscreenPageToggle()" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                    <i id="fullscreenPageToggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-expand-arrows-alt"></i>
                </div>
                <div id="PictureInPicturePage" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Picture-in-Picture the video mix" alt="Picture-in-Picture the page" aria-label="Picture-in-Picture"  onclick="PictureInPicturePageToggle()" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);"  class="float hidden" style="cursor: pointer;">
                    <i id="PictureInPicturePageToggle" onmousedown="event.preventDefault(); event.stopPropagation();"  class="toggleSize las la-external-link-square-alt"></i>
                </div>
                <div id="flipcamerabutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Cycle the Cameras"  onclick="cycleCameras()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Cycle Cameras" alt="Cycle the Cameras">
                    <i id="settingstoggle" class="toggleSize las la-sync-alt"></i>
                </div>

                 <div id="blackoutmode" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Enter black-out mode"  onclick="blackoutMode()" class="float hidden"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Black out mode" alt="Enter black-out mode">
                    <i id="blackouttoggle" class="toggleSize las la-moon"></i>
                </div>

                <div id="obscontrolbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="OBS Remote Controller; start/stop and change scenes." onclick="toggleOBSControls();" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="Remote OBS control menu" alt="Toggle the Remote OBS Controls Menu">
                    <i id="obscontroltoggle" class="toggleSize las la-gamepad"></i>
                </div>

                <div id="roomsettingsbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Room Settings" onclick="toggleRoomSettings();" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" alt="Toggle the Room Settings Menu" aria-label="Room settings menu">
                    <i id="roomsettingstoggle" class="toggleSize las la-users-cog"></i>
                </div>

                <div id="settingsbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Your audio and video Settings"  onclick="toggleSettings()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" alt="Toggle Settings Menu" aria-label="Settings menu">
                    <i id="settingstoggle" class="toggleSize las la-cog"></i>
                </div>

                <div id="hangupbutton"  onmousedown="event.preventDefault(); event.stopPropagation();" title="Hangup the Call" aria-label="Hang up" alt="Hangup the Call" onclick="hangup()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" >
                    <i class="toggleSize las la-phone rotate225" aria-hidden="true"></i>
                </div>
                <div id="raisehandbutton" onmousedown="event.preventDefault(); event.stopPropagation();"  data-raised="0" title="Alert the host you want to speak"  aria-label="Raise hand" alt="Alert the host you want to speak" tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" onclick="raisehand()" class="hidden float" style="cursor: pointer;">
                    <i class="toggleSize las la-hand-paper" style="position: relative; right: 1px;" aria-hidden="true"></i>
                </div>

                <div id="hangupbutton2" onmousedown="event.preventDefault(); event.stopPropagation();"  title="Cancel the Director's Video/Audio"  onclick="hangup2()" class="hidden float"  tabindex="2" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" style="cursor: pointer;" aria-label="stop publishing audio and video" alt="Disconnect Direcotor's cam">
                    <i class="toggleSize las la-phone rotate225" aria-hidden="true"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Mini Task Bar -->
    <span id="miniTaskBar" style="float: right; bottom: 0px;right:0; position:fixed; display:flex;">
        <div id="closedList_connectUsers" class="hidden"  onclick="getById('connectUsers').classList.remove('hidden');getById('closedList_connectUsers').classList.add('hidden');">
            <i class="las la-theater-masks"></i>
        </div>
    </span>

    <!-- Main Menu -->
    <div id="mainmenu" class="row" style="opacity: 0;">
        <div id="container-1" title="Add Group Chat to OBS"  alt="Add Group Chat to OBS" tabindex="1" role="button" aria-pressed="false" onkeyup="enterPressedClick(event,this);" class="column columnfade pointer card" style=" overflow-y: auto;">

            <h2>
                <span data-translate="add-group-chat">Create a Room</span>
            </h2>
            <div class="container-inner">
                <br />
                <br />
                <span data-translate="rooms-allow-for">Rooms allow for group-chat and the tools to manage multiple guests.</span>
                <br />
                <span class="section">
                    <table style="padding-bottom:0;margin-bottom:0;">
                     <tr>
                        <th style="text-align:right;" class="labelLarge">
                            <b>
                            <span data-translate="room-name">Room Name</span>:
                            </b>
                        </th>
                        <th class="labelSmall"></th>
                        <th style="text-align:left;" >
                            <div class="labelSmall">
                                <b>
                                    <span data-translate="room-name">Room Name</span>:
                                </b>
                            </div>
                            <input type="text" autocorrect="off" autocapitalize="none"  id="videoname1" placeholder="Enter a room name here" onkeydown="checkStrengthRoom(event, 'securityLevelRoom');" onchange="checkStrengthRoom(event, 'securityLevelRoom');" onkeyup="enterPressed(event, createRoom);" maxlength="30" style="max-width: 431px;width: 100%;font-size: 110%; padding: 5px;" />
                            <button ontouchstart="getById('videoname1').value = session.generateRandomString();getById('securityLevelRoom').style.display='none';" onmousedown="getById('videoname1').value = session.generateRandomString();getById('securityLevelRoom').style.display='none';" title="Generate a random room name" class="randomRoomName"><i class="las la-random"></i></button>
                            <div id="securityLevelRoom" style="display:none;margin-top:3px;position:relative;top:3px;font-size:0.8em;"></div>
                        </th>
                    </tr>
                    <tr>
                        <th style="text-align:right;" class="labelLarge">
                            <b>
                                <span data-translate="password-input-field">Password</span>:
                            </b>
                        </th>
                        <th class="labelSmall"></th>
                        <th  style="text-align:left;">
                            <div class="labelSmall">
                                <b>
                                    <span data-translate="password-input-field">Password</span>:
                                </b>
                            </div>
                            <input type="text" autocorrect="off" autocapitalize="none" id="passwordRoom" placeholder="Optional room password here" onkeydown="checkStrengthRoom(event, 'securityLevelRoom');" onchange="checkStrengthRoom(event, 'securityLevelRoom');"  onkeyup="enterPressed(event, createRoom);" style="max-width: 431px;width: 100%;font-size: 110%; padding: 5px;" />
                        </th>
                    </tr>
                    <tr >

                        <th style="text-align:right; padding: 5px; padding-top: 20px;">
                            <input id="broadcastFlag" type="checkbox" title="For large group rooms, this option can reduce the load on remote guests substantially" />
                        </th><th style="text-align:left;; padding-top: 20px;">
                        <b>
                            <span data-translate="guests-only-see-director"  style="cursor: help;" title="For large group rooms, this option can reduce the load on remote guests substantially" >The guests can see the director, but not other guests' videos</span>
                        </b>
                        </th>
                    </tr>
                    <tr>

                        <th style="text-align:right; padding: 5px;; padding-bottom: 20px;">
                            <input id="showdirectorFlag" type="checkbox" title="The director will be visible in scenes as if a performer themselves." />
                        </th><th style="text-align:left;; padding-bottom: 20px;">
                        <b>
                            <span data-translate="scenes-can-see-director" style="cursor: help;" title="If checked, the director can be added to scenes as if a guest. Otherwise, the director will never appear in a scene." >The director will be performing as well, appearing in group scenes</span>
                        </b>
                        </th>
                    </tr>
                    </table>
                    <button onclick="createRoom()"  class="gobutton gowebcam" style="width: 470px;display: block;margin: 20px auto;" alt="Enter the room as the group's director" title="You'll enter as the room's director">
                        <span data-translate="enter-the-rooms-control">Enter the room's Control Center in the director's role</span>
                    </button>
                </span>
            </div>
        </div>

        <!-- Add Camera Section -->
        <div id="container-3"   alt="Add your Camera to OBS" tabindex="1" role="button" aria-pressed="false" class="column columnfade pointer card" onclick="previewWebcam()" style=" overflow-y: auto;">
            <h2 id="add_camera">
                <span data-translate="add-your-camera">Add your Camera to OBS</span>
            </h2>
            <div class="container-inner" id="add_camera_inner">
                <br />
                <p>
                    <video id="previewWebcam" class="previewWebcam task" aria-hidden="true" title="Right-click this video for additional options" data-menu="context-menu-video" oncanplay="updateStats();" controlsList="nodownload" muted autoplay playsinline ></video>
                </p>
                <div id="infof"></div>
                <button onclick="this.disabled=true;setTimeout(function(){requestBasicPermissions();},20);" id="getPermissions" style="display:none;" data-ready="false" >
                    <span data-translate="ask-for-permissions">Allow Access to Camera/Microphone</span>
                </button>
                <span style="display:block;">
                    <button onclick="publishWebcam(this)" title="Start streaming (Alt + s)" aria-label="Start streaming (Alt + s)" role="button" aria-pressed="false" tabindex="1" id="gowebcam" class="gowebcam" alt="Start Streaming  (Alt + s)" disabled data-audioready="false" data-ready="false" >
                        <span data-translate="waiting-for-camera">Waiting for Camera to Load</span>
                    </button>
                </span>

                <!-- Video Menu -->
                <div id="videoMenu" class="videoMenu" aria-hidden="true">
                    <div class="title">
                        <i class="las la-video"></i><span data-translate="video-source"> Video Source </span>
                    </div>
                    <span style="display:inline-block;padding-top: 5px;">
                        <select id="videoSourceSelect" tabindex="1" title="Video source list"></select>
                        <span id="gear_webcam" onclick="toggle(document.getElementById('videoSettings'));">
                            <i class="las la-cog" style="font-size: 140%; vertical-align: middle;" aria-hidden="true"></i>
                        </span>
                    </span>
                </div>

                <!-- Audio Menu -->
                <div id="audioMenu" class="form-group multiselect" alt="tip: Hold CTRL (command) to select Multiple" title="tip: Hold CTRL (command) to select Multiple">
                    <a id="multiselect-trigger" class="form-control multiselect-trigger" data-state="1">
                        <div class="title">
                            <i class="las la-microphone-alt"></i><span data-translate="select-audio-source"> Audio Source(s) </span>
                            <i id='chevarrow1' class="chevron bottom" aria-hidden="true"></i>
                            <div class="meter" id="meter1"></div><div class="meter2" id="meter2"></div>
                        </div>
                    </a>
                    <ul id="audioSource" class="multiselect-contents" >
                        <li>
                            <input type="checkbox" id="multiselect1" name="multiselect1" style="display: none;" checked value="ZZZ" />
                            <label for="multiselect1">
                                <span data-translate="no-audio"> No Audio</span>
                            </label>
                        </li>
                    </ul>
                </div>

                <div class="outer close" role="button" aria-pressed="false" title="Go back">
                    <div class="inner">
                        <label class="labelclass">
                            <span data-translate="back">Back</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Configuration and Includes -->
    <script type="text/javascript">
        // Configuration settings - can be customized via PHP
        <?php
        // You can add PHP logic here to dynamically set JavaScript variables
        // For example, based on user preferences, database settings, etc.
        ?>

        // Default configuration (same as original default.html)
        // session.configuration.iceServers = [];
        // session.wss = "wss://wss.vdo.ninja:443"; // US-East (Default)

        // Custom PHP-driven configurations can be added here
        // Example: session.roomid = "<?php echo isset($_GET['room']) ? htmlspecialchars($_GET['room']) : ''; ?>";

    </script>
    <script type="text/javascript" crossorigin="anonymous" id="lib-js" src="./lib.js?ver=1294"></script>
    <script type="text/javascript" crossorigin="anonymous" id="main-js" src="./main.js?ver=954"></script>
    <!-- <script type="text/javascript" crossorigin="anonymous"  src="./trace.js?ver=1"></script>!-->
</body>
</html>

# VDO.Ninja Modern PHP Interface

This is a modern, modular PHP interface for VDO.ninja that separates the original functionality into clean, manageable components.

## File Structure

### Main Files
- **app.php** - Main application file with modern UI and navigation
- **create-room.php** - Room creation component (container-1 from original)
- **add-camera.php** - Camera addition component (container-3 from original, hidden by default)
- **director.php** - Director control center component

### Dependencies
All original CSS and JavaScript files are preserved and referenced:
- `main.css` - Main stylesheet
- `webrtc.js` - Core WebRTC functionality
- `lib.js` - Library functions
- `main.js` - Main application logic
- `thirdparty/` - Third-party libraries (adapter.js, CodecsHandler.js, aes.js)

## Features

### Modern UI Enhancements
- **Sleek Design**: Glass-morphism cards with backdrop blur effects
- **Responsive Navigation**: Clean navigation between sections
- **Smooth Animations**: Hover effects and transitions
- **Hidden by Default**: Camera section is hidden initially as requested

### Modular Structure
- **Separated Components**: Each major section is in its own PHP file
- **Easy Maintenance**: Components can be updated independently
- **Reusable**: Components can be included in other pages if needed

### Navigation
- **Create Room**: Always visible, main functionality
- **Add Camera**: Hidden by default, can be shown via toggle button
- **Director**: Hidden by default, appears when director mode is activated

## Usage

1. **Access the Interface**: Open `app.php` in your web browser
2. **Create a Room**: Use the main "Create Room" section (always visible)
3. **Show Camera Options**: Click "Show Camera Options" button to reveal the camera section
4. **Director Mode**: Director controls appear when entering director mode

## Technical Details

### JavaScript Functions
- `showSection(sectionName)` - Shows a specific section and hides others
- `toggleCameraSection()` - Toggles the camera section visibility
- `hideSection(sectionName)` - Hides a specific section

### CSS Classes
- `.section-card` - Modern card styling for each section
- `.hidden-section` - Hides sections when not active
- `.nav-btn` - Navigation button styling
- `.toggle-btn` - Toggle button styling

### PHP Includes
Each section is loaded via PHP include:
```php
<?php include 'create-room.php'; ?>
<?php include 'add-camera.php'; ?>
<?php include 'director.php'; ?>
```

## Customization

### Adding New Sections
1. Create a new PHP file for your component
2. Add a new section div in `app.php`
3. Add navigation button if needed
4. Update JavaScript functions for navigation

### Styling
- Modify the CSS in the `<style>` section of `app.php`
- All original VDO.ninja styles are preserved via `main.css`
- Modern enhancements are additive, not replacing

### Functionality
- All original VDO.ninja functionality is preserved
- JavaScript functions from original files are still available
- WebRTC functionality remains unchanged

## Browser Compatibility

- Same as original VDO.ninja
- Internet Explorer is blocked (as in original)
- Modern browsers with WebRTC support required

## Notes

- The "Add Camera" section is hidden by default as requested
- All original functionality is preserved
- Modern UI is layered on top of existing functionality
- Director and room sections are properly separated
- All CSS and JS references are maintained for full compatibility

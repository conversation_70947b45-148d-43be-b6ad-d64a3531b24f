<div class="container-inner" id="add_camera_inner">
    <br />
    <p>
        <video id="previewWebcam" class="previewWebcam task" aria-hidden="true" title="Right-click this video for additional options" data-menu="context-menu-video" oncanplay="updateStats();" controlsList="nodownload" muted autoplay playsinline ></video>
    </p>
    <div id="infof"></div>
    <button onclick="this.disabled=true;setTimeout(function(){requestBasicPermissions();},20);" id="getPermissions" style="display:none;" data-ready="false" >
        <span data-translate="ask-for-permissions">Allow Access to Camera/Microphone</span>
    </button>
    <span style="display:block;">
        <button onclick="publishWebcam(this)" title="Start streaming (Alt + s)" aria-label="Start streaming (Alt + s)" role="button" aria-pressed="false" tabindex="1" id="gowebcam" class="gowebcam" alt="Start Streaming  (Alt + s)" disabled data-audioready="false" data-ready="false" >
            <span data-translate="waiting-for-camera">Waiting for Camera to Load</span>
        </button>
    </span>
    <div id="consentWarning" class="startupWarning hidden">
        <i class="las la-exclamation-circle"></i>
        <p><span data-translate="privacy-disabled">Privacy warning: The director will be able to remotely change your camera, microphone, and URL.</span></p>
    </div>
    <div id="guestTips" style="display:none" aria-hidden="true">
        <p data-translate="for-the-best-possible-experience-make-sure">For the best possible experience, make sure</p>
        <span><i class="las la-plug"></i><span data-translate="your-device-is-powered">Your device is powered</span></span>
        <span><i class="las la-ethernet"></i><span data-translate="your-connection-is-hardwired-instead-of-wifi">Your connection is hardwired instead of wifi</span></span>
        <span><i class="las la-headphones"></i><span data-translate="you-are-using-headphones-earphones">You are using headphones / earphones</span></span>
    </div>
    <div id="videoMenu" class="videoMenu" aria-hidden="true">
        <div class="title">
            <i class="las la-video"></i><span data-translate="video-source"> Video Source </span>
        </div>
        <span style="display:inline-block;padding-top: 5px;">
            <select id="videoSourceSelect" tabindex="1" title="Video source list"></select>
            <span id="gear_webcam" onclick="toggle(document.getElementById('videoSettings'));">
                <i class="las la-cog" style="font-size: 140%; vertical-align: middle;" aria-hidden="true"></i>
            </span>
        </span>
        <div id="cameraTip1" class="cameraTip hidden">
            <i class="las la-info-circle"></i>
            <p><span id="cameraTipContext1"></span></p>
        </div>
    </div>
    <br />
    <center>
        <div id="videoSettings" style="display: none;" aria-hidden="true">
            <form id="webcamquality">
                <span class="hidden">
                    <input type="radio" id="4kquality" alt="2160p60 video capture" name="resolution" value="-2" />
                    <label for="4kquality">
                        <span data-translate="up-to-4k">4K</span>
                    </label> |
                </span>
                
                <input type="radio" id="fullhd" alt="1080p60 video capture" name="resolution" value="0" />
                <label for="fullhd">
                    <span data-translate="max-resolution">High Resolution</span>
                </label> |
                
                <input type="radio" checked id="halfhd" alt="720p60 video capture"  name="resolution" value="1" />
                <label for="halfhd">
                    <span data-translate="balanced">Balanced</span>
                </label> |
                
                <input type="radio" id="nothd" name="resolution" alt="360p30 video capture"  value="2" />
                <label for="nothd">
                    <span data-translate="smooth-cool">Smooth and Cool</span>
                </label>
                <div id="webcamstats" style="padding: 5px 0 0 0;"></div>
            </form>
        </div>
    </center>
    <div id="audioMenu" class="form-group multiselect" alt="tip: Hold CTRL (command) to select Multiple" title="tip: Hold CTRL (command) to select Multiple">
        <span class='gear_microphone hidden'>
            <input type="checkbox" id='micStereoMonoInput' alt="Mono microphone audio"  onchange="toggleMonoStereoMic(this);">Mono
        </span>
        <a id="multiselect-trigger" class="form-control multiselect-trigger" data-state="1">
            <div class="title">
                <i class="las la-microphone-alt"></i><span data-translate="select-audio-source"> Audio Source(s) </span>
                <i id='chevarrow1' class="chevron bottom" aria-hidden="true"></i>
                <div class="meter" id="meter1"></div><div class="meter2" id="meter2"></div>
            </div>
        </a>
        <ul id="audioSource" class="multiselect-contents" >
            <li>
                <input type="checkbox" id="multiselect1" name="multiselect1" style="display: none;" checked value="ZZZ" />
                <label for="multiselect1">
                    <span data-translate="no-audio"> No Audio</span>
                </label>
            </li>
        </ul>
        <div id="audioTip1" class="cameraTip hidden">
            <i class="las la-info-circle"></i>
            <p><span id="audioTipContext1"></span></p>
        </div>
    </div>
    <br style="line-height: 0;" />
    <div id="headphonesDiv" class="audioMenu hidden" aria-hidden="true">
        <div class="title">
            <i class="las la-headphones"></i><span data-translate="select-output-source"> Audio Output Destination</span><button onclick="playtone()" title="Play a sound out of the selected audio playback device" class="testtonebutton" type="button">Test</button>
        </div>
        <select id="outputSource" alt="Audio output device" ></select>
        <div id="headphoneTip1" class="cameraTip hidden">
            <i class="las la-info-circle"></i>
            <p><span id="headphoneTipContext1"></span></p>
        </div>
        <div id="audioTipSR" class="cameraTip hidden">
            <i class="las la-exclamation-circle"></i>
            <p><span id="audioTipContextSR"></span></p>
        </div>
    </div>
    <br style="line-height: 0;" />
    <div id="avatarDiv" class="hidden" aria-hidden="true">
        <div class="title">
            <i class="las la-robot"></i><span data-translate="select-avatar-image"> Default Avatar / Placeholder Image </span>
        </div>
        <div id="selectAvatarImage" style="margin-top:10px;">
            <img src="./media/avatar.webp" crossOrigin="Anonymous" loading="lazy" id="defaultAvatar1" style="max-width:130px;max-height:73.5px;display:inline-block;margin:10px;cursor:pointer;" onclick="changeAvatarImage(event, this);"/>
            <label class="selected" id="noAvatarSelected" style="width:130px;display:inline-block;text-align: center; cursor:pointer;">
              <i class="las la-minus-circle" style="font-size: 3em;"></i><br />No Image Selected
              <button onclick="changeAvatarImage(event, this)" style="position: fixed; top: -100em; margin-left:10px; border:1px solid #555;"></button>
            </label>
            <label style="width:130px;display:inline-block; text-align: center; cursor:pointer;">
              <i class="las la-hdd" style="font-size: 3em;"></i><br />Select Local Image
              <input type="file" onchange="changeAvatarImage(event, this)" accept="image/*" style="position: fixed; top: -100em; margin-left:10px; border:1px solid #555;"> 
            </label>
        </div>
    </div>
    
    
    <br style="line-height: 0;" />
    <div id="addPasswordBasic">
        <div class="title" title="Add an optional password">
            <i class="las la-key"></i><span data-translate="add-a-password"> Add a Password</span>
        </div>
        <input type="text" id="passwordBasicInput" title="Enter an optional password here" placeholder="optional"/>
    </div>
    
    <br style="line-height: 0;" />
    <div id="rememberStreamID" class="hidden" style="display:inline-block;" title="Remember and reuse the provided stream ID on each visit">
        <br />
        <br style="line-height: 0;" />
        Remember Stream ID: <input type="checkbox" id="rememberStreamIDcheck" checked="true" />
    </div>
    
    <div id="SafariWarning" class="startupWarning hidden" aria-hidden="true" title="Consider using Chrome instead of Safari">
        <i class="las la-exclamation-circle"></i>
        <p><span data-translate="use-chrome-instead">Consider using a Chromium-based browser instead.<br />
         Safari is more prone to having audio issues</span></p>
    </div>
    
    <div id="oldiOSWarning" class="startupWarning hidden" title="Please update your version of iOS for best performance">
        <i class="las la-exclamation-circle"></i>
        <p><span data-translate="update-your-device">We've detected that you are using an old version of Apple iOS.<br /><br />Please consider updating if facing issues.</span></p>
    </div>
    
</div>
<div class="outer close" role="button" aria-pressed="false" title="Go back">
    <div class="inner">
        <label class="labelclass">
            <span data-translate="back">Back</span>
        </label>
    </div>
</div>
